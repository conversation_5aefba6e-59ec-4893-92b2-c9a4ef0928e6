const mongoose = require('mongoose');

const StudentSchema = new mongoose.Schema({
  name: {
    type: String,
    trim: true
  },
  mobile: {
    type: String,
    match: /^[0-9]{10}$/
  },
  password: { 
    type: String,
  },
  email: {
    type: String,
    lowercase: true,
    match: /^\S+@\S+\.\S+$/
  },
  dob: {
    type: Date,
  },
  roomNo: {
    type: String,
  },
  rollNo: {
    type: Number,
  },
  instituteName: {
    type: String,
  },
  semester: {
    type: Number,
    min: 1,
    max: 8
  },
  branch: {
    type: String,
  },
  address: {
    type: String,
  },
  parentMobile: {
    type: String,
    match: /^[0-9]{10}$/
  },
  role: {
    type: String
  },
  status: {
    type: String
  },
  isattendance:{
    type: Boolean,
    default: false
  }
}, {
  timestamps: true
});

module.exports = mongoose.model('Student', StudentSchema);
