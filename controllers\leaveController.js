const Student = require("../model/student.model")
const Leave = require("../model/leave.model");
const Attendance = require('../model/slotAttendance.model');



exports.applybyadmin = async (req, res) => {

  try {
    const { studentId, fromDate,rollNo, toDate, reason, name,status } = req.body;

    const newLeave = new Leave({
      studentId,
      name,
      rollNo,
      fromDate,
      toDate,
      reason,
      name,
      status
    });

    await newLeave.save();
    res.status(201).json({ 
      error:false,      
      message: 'Leave applied successfully'
     });
  } catch (error) {
    res.status(500).json({ error:true,message: 'Error applying for leave' });
  }

}
exports.applybystudent = async (req, res) => {

  try {
    
    const {  fromDate, toDate, reason } = req.body;
    
    const studentId = req.user._id;
    const name = req.user.name;
    const rollNo = req.user.rollNo;

    const newLeave = new Leave({
      studentId,
      name,
      rollNo,
      fromDate,
      toDate,
      reason,
      name,
      status: "pending"
    });

    await newLeave.save();
    res.status(201).json({ 
      error:false,      
      message: 'Leave applied successfully'
     });
  } catch (error) {
    res.status(500).json({ error:true,message: 'Error applying for leave' });
  }

}

exports.allRequests = async (req, res) => {

  try {
    const status = req.params.status;

    if (!status) {
      return res.status(200).json({
        error: true,
        message: "Status parameter is required"
      });
    }

    const requestbystatus = await Leave.find({status:status})

    if (!requestbystatus || requestbystatus.length === 0) {
      return res.status(404).json({
        error: true,
        message: `No users found with status: ${status}`
      });
    }    

    const result=  requestbystatus.map(user => ({
      _id: user._id,
      studentId: user.studentId,
      name: user.name,
      initials: user.name.split(' ').map(n => n[0]).join(''),
      rollNo: user.rollNo,
      fromDate: user.fromDate,
      toDate: user.toDate
    }))

    res.json({
      error:false,
      result
    })
  } catch (err) {
    console.error('Error in usersbystatus:', err);
    return res.status(500).json({
      error: true,
      message: "Error in get allrequest"
    });
  }
}

exports.allusers = async (req,res) =>{

  try {

    const users = await Student.find({status:"active"})

    if(!users) return res.status(404).json({error:true,message:"Users not found!"})

    const result = users.map(item=>({
      _id: item._id,
      name: item.name,
      rollNo: item.rollNo,
    }))

    return res.status(200)
    .json({
      error:false,
      result
    })
    
  } catch (error) {
    return res.status(500).json({
      error: true,
      message: "Error get allusers"
    });
  }
}

exports.oneuser = async (req,res) =>{
  try {
    const id = req.params.id

    const user = await Leave.findOne({_id:id})

    if(!user) return res.status(404).json({error:true,message:"User not found!"})

    return res.status(200)
    .json({
      error:false,
      user
    })

  } catch (error) {
    return res.status(500).json({
      error: true,
      message: "Error get user"
    });
  }
}

exports.updateleave = async (req, res) => {

  try {
    const {fromDate,reason,status,toDate} = req.body;
    
    const leave = await Leave.findById(req.params.id);

    if(!leave) return res.status(404).json({ error:true, message: 'Leave not found' });

    leave.reason = reason
    leave.fromDate = fromDate
    leave.toDate = toDate
    leave.status = status

    await leave.save();   

    res.json({ error:false,message: 'Leave approved and attendance updated.' });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error:true,message:'Server error' });
  }
}

exports.allReqbystudent = async (req,res)=>{

   try {
    const status = req.params.status;
    const id = req.user._id

    if (!status) {
      return res.status(400).json({
        error: true,
        message: "Status parameter is required"
      });
    }

    const requestbystatus = await Leave.find({studentId:id,status:status})

    if (!requestbystatus || requestbystatus.length === 0) {
      return res.status(200).json({
        error: true,
        message: `No users found with status: ${status}`
      });
    }    

    const result=  requestbystatus.map(user => ({
      _id: user._id,
      initials: user.name.split(' ').map(n => n[0]).join(''),
      reason: user.reason,
      fromDate: user.fromDate,
      toDate: user.toDate,
      
    }))

    res.json({
      error:false,
      result
    })
  } catch (err) {
    console.error('Error in usersbystatus:', err);
    return res.status(500).json({
      error: true,
      message: "Error in get allrequest"
    });
  }

}

