const Student = require("../model/student.model");
const bcrypt = require("bcryptjs");

exports.adminregister = async (req,res)=>{
  
    try {

      const {form} = req.body;
      const password = form.password;

      const hashed = await bcrypt.hash(password, 10);

      const newStudent = new Student({
      name: form.name,
      email: form.email,
      mobile: form.mobile,
      password: hashed,
      dob: form.dob,
      instituteName: form.instituteName,
      semester: form.semester,
      branch: form.branch,
      address: form.address,
      parentMobile: form.parentMobile,
      roomNo: form.roomNo,
      rollNo: form.rollNo,
      role: form.role, // Make sure to include userId if required
      status: form.status || "pending", // Default to "pending" if not provided
      isattendance: form.isattendance
    });

    await newStudent.save();
      
    
    res.status(201)
    .json({
      error:false,
      status: form.status

    });
  } catch (err) {
    return res.status(500).json({ 
      error: true,
      message: "Error fetching users" });
  }

}

exports.usersbystatus = async (req,res)=>{

  try{

    const status = req.query.status;

    if (!status) {
      return res.status(400).json({
        error: true,
        message: "Status parameter is required"
      });
    }
    
    if(status == "inactive"){

    }

      const users = await Student.find({status: status });

      if (!users || users.length === 0) {
      return res.status(404).json({
        error: true,
        message: `No users found with status: ${status}`
      });
    }
    
     return res.status(200).json({
      error: false,
      users: users.map(user => ({
        id: user._id,
        name: user.name,
        initials: user.name.split(' ').map(n => n[0]).join(''),
        roll: user.rollNo,
        room: user.roomNo
      }))
    });

  }catch (err) {
    console.error('Error in usersbystatus:', err);
    return res.status(500).json({ 
      error: true,
      message: "Error fetching users",
      details: err.message 
    });
  }
}

exports.usersprofile = async (req,res)=>{
  try{

    const userId=req.params.id;

    if (!userId) {
      return res.status(400).json({
        error: true,
        message: "User ID is required"
      });
    }    

    const user = await Student.findById(userId);

    if (!user) {
      return res.status(404).json({
        error: true,
        message: "User not found"
      });
    }

    return res.status(200).json({
      error: false,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        mobile: user.mobile,
        dob: user.dob,
        instituteName: user.instituteName,
        semester: user.semester,
        branch: user.branch,
        address: user.address,
        parentMobile: user.parentMobile,
        roomNo: user.roomNo,
        rollNo: user.rollNo,
        role: user.role,
        status: user.status,
        isattendance: user.isattendance
      }
    });

    }catch (err) {
    console.error('Error in usersprofile:', err);
    return res.status(500).json({ 
      error: true,
      message: "Error fetching user profile",
      details: err.message 
    });
  }
}

exports.editusersprofile = async (req,res)=>{
  try{

    const userId=req.params.id;
    const form = req.body.form;

    if (!userId) {
      return res.status(400).json({
        error: true,
        message: "User ID is required"
      });
    }    

    const user = await Student.findById(userId);

    if (!user) {
      return res.status(404).json({
        error: true,
        message: "User not found"
      });
    }

    user.name = form.name,
    user.email =  form.email,
    user.mobile = form.mobile,
    user.dob = form.dob,
    user.instituteName = form.instituteName,
    user.semester = form.semester,
    user.branch = form.branch,
    user.address = form.address,
    user.parentMobile = form.parentMobile,
    user.roomNo = form.roomNo,
    user.rollNo = form.rollNo,
    user.role = form.role,
    user.status = form.status,
    user.isattendance = form.isattendance

    if(form.password){
      const hashed = await bcrypt.hash(form.password, 10);
      user.password = hashed
    }

    await user.save();


    return res.status(200).json({
      error: false,
      message: "Update Successfully..."
    });

    }catch (err) {
    console.error('Error in usersprofile:', err);
    return res.status(500).json({ 
      error: true,
      message: "Error fetching user profile",
      details: err.message 
    });
  }
}