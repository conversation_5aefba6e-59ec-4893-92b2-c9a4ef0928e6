const Student = require("../model/student.model");
const bcrypt = require("bcryptjs");

exports.register = async (req,res)=>{
  
    try {
      const {form} = req.body;
    const userId = req.user._id;
    const userStatus = req.user.status;

    if (userStatus !== "new") {
      return res.status(400).json({ 
        error: true, 
        message: "Form can only be submitted once" 
      });
    }

    const userInfo = await Student.findById(userId);
    if (!userInfo) {
      return res.status(404).json({
        error: true,
        message: "User not found"
      });
    }

   userInfo.name = form.name;
    userInfo.email = form.email;
    userInfo.dob = form.dob;
    userInfo.instituteName = form.instituteName;
    userInfo.semester = form.semester;
    userInfo.branch = form.branch;
    userInfo.address = form.address;
    userInfo.parentMobile = form.parentMobile;
    userInfo.status = "pending";

    try {
      await userInfo.save();
      return res.status(201).json({
        error: false,
        status: "pending"
      });
    } catch (saveError) {
      return res.status(500).json({
        error: true,
        message: "Error saving user information",
        details: saveError.message
      });
    }
  } catch (err) {
    res.status(400).json({ error: err.message });
  }

}

exports.profile = async (req,res)=>{
  try{

    const userId = req.user._id;

    if (!userId) {
      return res.status(400).json({
        error: true,
        message: "User ID is required"
      });
    }    

    const user = await Student.findById(userId);

    if (!user) {
      return res.status(404).json({
        error: true,
        message: "User not found"
      });
    }

    return res.status(200).json({
      error: false,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        mobile: user.mobile,
        dob: user.dob,
        instituteName: user.instituteName,
        semester: user.semester,
        branch: user.branch,
        address: user.address,
        parentMobile: user.parentMobile,
        roomNo: user.roomNo,
        rollNo: user.rollNo,
        role: user.role,
        status: user.status,
        isattendance: user.isattendance
      }
    });

    }catch (err) {
    console.error('Error in usersprofile:', err);
    return res.status(500).json({ 
      error: true,
      message: "Error fetching user profile",
      details: err.message 
    });
  }
}