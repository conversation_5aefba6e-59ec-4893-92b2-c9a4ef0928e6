const Student = require("../model/student.model");
const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");

exports.register = async (req, res) => {

  const { mobile, password } = req.body;

  //error 
  if (!mobile) {
    return res
      .status(400)
      .json({ error: true, message: "Mobile is required" });
  }
  if (!password) {
    return res
      .status(400)
      .json({ error: true, message: "password is required" });
  }

  try {
    //if user already exist
    const isStudent = await Student.findOne({ mobile: mobile });

    if(isStudent){
      return res.json({
          error: true,
          message: "User already exist",
        })
    }

    //make new user 
    const hashed = await bcrypt.hash(password, 10);
    const user = new Student({
      mobile,
      password: hashed,
      status: "new",
      role: "student"
    });
    await user.save();

    // const accessToken = jwt.sign({_id: user._id,
    //     role: user.role,
    //     status: user.status}, process.env.JWT_SECRET)

    res.status(201)
    .json({
      error: false,
      status: "new",
      role: "student",
      // accessToken,
      message: "Registration Successful"
    })

  } catch (error) {
    console.error("Registration error:", error);
    return res.status(500).json({
      error: true,
      message: "An error occurred during registration"
    });
  }
};

exports.login = async (req, res) => {
  const { mobile, password } = req.body;


  //error
  if (!mobile) {
    return res
      .status(400)
      .json({ error: true, message: "Mobile is required" });
  }
  if (!password) {
    return res
      .status(400)
      .json({ error: true, message: "password is required" });
  }

  try {

    //is user exist
    const userInfo = await Student.findOne({mobile:mobile});

    if(!userInfo){
      return res
      .status(400)
      .json({message: "User not found"})
    }

    const match = await bcrypt.compare(password, userInfo.password);

    if (!match) {
      return res
        .status(401)
        .json({ error: true, message: "Invalid credentials" });
    }

    const accessToken = jwt.sign(
      { _id: userInfo._id,
        role: userInfo.role,
        status: userInfo.status,
        name: userInfo.name,
        isattendance: userInfo.isattendance,
        rollNo: userInfo.rollNo
       }, 
      process.env.JWT_SECRET,
      { expiresIn: '1d' }
    );

    return res.json({
      error: false,
      message: "Login successful",
      accessToken,
      name: userInfo.name,
      role: userInfo.role,
      rollNo: userInfo.rollNo, 
      status: userInfo.status,
      isattendance: userInfo.isattendance
    })
   
  } catch (error) {
    console.error("Login error:", error);
    return res.status(500).json({
      error: true,
      message: "An error occurred during login"
    });
  }

};

exports.updatepassword =async(req,res)=>{

  try{
    const{ currentPassword,newPassword} = req.body;
    const userId = req.user._id;
    

    const user = await Student.findById(userId)
    

    const match = await bcrypt.compare(currentPassword,user.password)
    if(!match){
      return res
        .status(401)
        .json({ error: true, message: "Invalid credentials" });
    }

    const hashed = await bcrypt.hash(newPassword,10)

    user.password = hashed;

    await user.save();

    return res.json({
      error: false,
      message: "Update successfull.",
    })

  }catch (error) {
    console.error("Password Update error:", error);
    return res.status(500).json({
      error: true,
      message: "An error occurred during update"
    });
  }
}