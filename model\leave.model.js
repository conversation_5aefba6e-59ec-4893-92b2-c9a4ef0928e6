const mongoose = require('mongoose');

const LeaveSchema = new mongoose.Schema({
  studentId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Student',
    required: true
  },
  name:{
    type: String,
    require: true
  },
   rollNo: {
        type: Number,
        required: true
      },
  reason: {
    type: String,
    required: true,
    trim: true
  },
  fromDate: {
    type: String, // Format: "YYYY-MM-DD"
    required: true
  },
  toDate: {
    type: String, // Format: "YYYY-MM-DD"
    required: true
  },
  status: {
      type: String,
      enum: ['pending', 'approved', 'rejected'],
      default: 'pending'
    }
}, {
  timestamps: true
});

module.exports = mongoose.model('Leave', LeaveSchema);
