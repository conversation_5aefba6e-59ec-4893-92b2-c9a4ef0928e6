const jwt = require("jsonwebtoken");

const authMiddleware = (req, res, next) => {
  const accessToken = req.headers.authorization?.split(" ")[1];
  if (!accessToken) return res.status(401).json({ message: "Unauthorized" });
  
  try {
    const decoded = jwt.verify(accessToken, process.env.JWT_SECRET);
    req.user = decoded; // { id, email }
    next();
  } catch (err) {
    res.status(401).json({ message: "Invalid Token" });
  }
};

const adminAuthMiddleware = (req, res, next) =>{
const accessToken = req.headers.authorization?.split(" ")[1];
  if (!accessToken) return res.status(401).json({ message: "Unauthorized" });
  
  try {
    const decoded = jwt.verify(accessToken, process.env.JWT_SECRET);

    // Check if user is admin
    if (decoded.role !== 'admin') {
      return res.status(403).json({ 
        message: "Access denied. Admin privileges required." 
      });
    }
    
    req.user = decoded; // { id, email }
    next();
  } catch (err) {
    res.status(401).json({ message: "Invalid Token" });
  }
}
const attendanceMiddleware = (req, res, next) =>{
const accessToken = req.headers.authorization?.split(" ")[1];
  if (!accessToken) return res.status(401).json({ message: "Unauthorized" });
  
  try {
    const decoded = jwt.verify(accessToken, process.env.JWT_SECRET);

    // Check if user is admin
    if (decoded.role !== 'admin' && decoded.isattendance !== true) {
      return res.status(403).json({ 
        error:true,
        message: "Access denied. Admin privileges required." 
      });
    }
    
    req.user = decoded; 
    next();
  } catch (err) {
    res.status(401).json({ message: "Invalid Token" });
  }
}

module.exports = {
  authMiddleware,
  adminAuthMiddleware,
  attendanceMiddleware
};
