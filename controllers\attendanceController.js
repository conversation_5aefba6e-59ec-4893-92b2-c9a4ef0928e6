const Student = require("../model/student.model");
const SlotAttendance = require("../model/slotAttendance.model");
const Leave = require("../model/leave.model");
const Config = require("../model/config.model");

exports.slot = async (req, res) => {
  try {
    const { date, slot } = req.query;

    if (!date || !slot) {
      return res.status(400).json({
        error: true,
        message: "Date and slot are required",
      });
    }

    let attendanceSlot = await SlotAttendance.findOne({ date, slot });

    if(attendanceSlot)
      {
        if(attendanceSlot.isnewevent){

      const students = await Student.find({ status: "active" }, "_id rollNo");

      const leaveRecords = await Leave.find({
        status: "approved",
        fromDate: { $lte: date },
        toDate: { $gte: date },
      });

      const leaveSet = new Set(
        leaveRecords.map((leave) => leave.studentId.toString())
      );

      const attendance = students.map((student) => ({
        studentId: student._id,
        rollNo: student.rollNo,
        status: leaveSet.has(student._id.toString()) ? "leave" : "nomark",
      }));

      attendanceSlot.attendance = attendance;
      attendanceSlot.isnewevent = false;
      await attendanceSlot.save();
    }}

    if (!attendanceSlot ) {
      const students = await Student.find({ status: "active" }, "_id rollNo");

      const leaveRecords = await Leave.find({
        status: "approved",
        fromDate: { $lte: date },
        toDate: { $gte: date },
      });

      const leaveSet = new Set(
        leaveRecords.map((leave) => leave.studentId.toString())
      );

      const attendance = students.map((student) => ({
        studentId: student._id,
        rollNo: student.rollNo,
        status: leaveSet.has(student._id.toString()) ? "leave" : "nomark",
      }));

      attendanceSlot = new SlotAttendance({
        date,
        slot,
        attendance,
      });

      await attendanceSlot.save();
    }

    return res.status(200).json({
      error: false,
      message: "Attendance retrieved successfully",
      data: attendanceSlot.attendance,
    });
  } catch (err) {
    console.error("Error in /attendance/slot:", err);
    return res.status(500).json({
      error: true,
      message: "Error retrieving attendance",
    });
  }
};

exports.markattendance = async (req, res) => {
  try {
    const { changedUsers, date, slot } = req.body;

    if (!changedUsers || !Array.isArray(changedUsers) || changedUsers.length === 0 ) {
      return res.status(400).json({
        error: true,
        message: "No changes provided",
      });
    }

    let record = await SlotAttendance.findOne({ slot, date });

    if (!record) {
      return res.status(404).json({
        error: true,
        message: "No attendance records found for this date",
      });
    }

    const changedUsersMap = new Map();
    changedUsers.forEach((user) => {
      changedUsersMap.set(user._id.toString(), user.status);
    });

    // Update attendance records
    let updatedCount = 0;
    record.attendance.forEach((item) => {
      const itemId = item._id.toString();

      // Check if this user's status needs to be updated
      if (changedUsersMap.has(itemId)) {
        const newStatus = changedUsersMap.get(itemId);

        // Only update if status is actually different
        if (item.status !== newStatus) {
          item.status = newStatus;
          updatedCount++;
        }
      }
    });

    // Save the updated record
    await record.save();

    return res.status(200).json({
      error: false,
      message: "Attendance updated successfully",
    });
  } catch (err) {
    console.error("Error in usersbystatus:", err);
    return res.status(500).json({
      error: true,
      message: "Error updating attendance",
    });
  }
};

exports.newevent = async (req,res)=>{
  try {

    const {date,slot} = req.body;

    if (!date || !slot) {
      return res.status(400).json({
        error: true,
        message: "Date and slot are required",
      });
    }

    let attendanceSlot = await SlotAttendance.findOne({ date, slot });

    if (attendanceSlot) return res.status(200).json({
      error: true,
      message: "Event already exists"
    });

    if (!attendanceSlot) {

      attendanceSlot = new SlotAttendance({
        date,
        slot,
        isnewevent:true,
      });

      await attendanceSlot.save();
    } 

    return res.status(200).json({
      error: false,
      message: "New Event added successfully"
    });
    
  } catch (error) {
    return res.status(500).json({
      error: true,
      message: "Error in making new event",
    });
  }
}

// event control

exports.getevent = async (req,res)=>{
  try {
    const date = req.query.date;

    const event = await SlotAttendance.find({date:date});

    if(event.length === 0) return res.status(200).json({error:true,message:`NewEvent not Found on Date:${date}`})

      const result = {
        date: date,
        slots: []
      }

      event.forEach(item => {
       if (!result.slots.includes(item.slot)) {
    result.slots.push(item.slot);
       }
        });

      return res.status(200)
      .json({
        error:false,
        result
      })
    
  } catch (error) {
    return res.status(500).json({
      error: true,
      message: "Error in fetch new event",
    });
    
  }
}
