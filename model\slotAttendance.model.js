const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const SlotAttendanceSchema = new Schema({
  date: {
    type: String,
    required: true
  },
  slot: {
    type: String,
    required: true,
  },
  isnewevent:{
    type:Boolean,
    default:false
  },
  attendance: [
    {
      studentId: {
        type: Schema.Types.ObjectId,
        ref: 'Student',
        required: true
      },
      rollNo: {
        type: Number,
        required: true
      },
      status: {
        type: String,
        enum: ['present', 'absent', 'leave', 'nomark'],
        default: 'nomark'
      }
    }
  ]
}, {
  timestamps: true
});

// Ensure only one document per (date + slot)
SlotAttendanceSchema.index({ date: 1, slot: 1 }, { unique: true });

module.exports = mongoose.model("SlotAttendance", SlotAttendanceSchema);
